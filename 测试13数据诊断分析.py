"""
测试13数据诊断分析脚本
====================

功能：
1. 分析测试13数据中下午2点后的数据缺失问题
2. 识别具体的传感器故障或数据异常
3. 分析正面温度与背面温度的关系
4. 为数据修复提供依据

作者：AI助手
日期：2025年
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
from datetime import datetime, timedelta
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

def analyze_test13_data():
    """分析测试13数据的完整性和异常情况"""
    
    print("=== 测试13数据诊断分析 ===\n")
    
    # 读取数据
    try:
        df = pd.read_csv('13_fixed_headers.csv')
        print(f"成功读取数据，共 {len(df)} 个数据点")
    except Exception as e:
        print(f"读取数据文件时出错: {e}")
        return
    
    # 计算真实时间
    total_duration_seconds = df['时间'].max()
    file_creation_time = datetime(2025, 7, 7, 17, 26, 0)
    test_start_time = file_creation_time - timedelta(seconds=total_duration_seconds)
    df['真实时间'] = df['时间'].apply(lambda x: test_start_time + timedelta(seconds=x))
    
    print(f"测试开始时间: {test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试结束时间: {file_creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 找到下午2点（14:00）对应的数据点
    target_time = datetime(2025, 7, 7, 14, 0, 0)
    df['时间差'] = abs((df['真实时间'] - target_time).dt.total_seconds())
    target_index = df['时间差'].idxmin()
    
    print(f"\n下午2点最接近的数据点:")
    print(f"索引: {target_index}")
    print(f"实际时间: {df.loc[target_index, '真实时间'].strftime('%H:%M:%S')}")
    
    # 分析所有温度参数
    temp_columns = [
        '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度',
        '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度',
        '皮革表面温度', '皮革背面温度', '环境温度'
    ]
    
    print(f"\n=== 数据完整性分析 ===")
    
    # 分析整体数据缺失情况
    for col in temp_columns:
        if col in df.columns:
            total_missing = df[col].isna().sum()
            missing_after_2pm = df.loc[target_index:, col].isna().sum()
            print(f"{col}:")
            print(f"  总缺失数据点: {total_missing}")
            print(f"  下午2点后缺失: {missing_after_2pm}")
            
            # 检查异常值（可能的传感器故障）
            temp_data = df[col].dropna()
            if len(temp_data) > 0:
                mean_temp = temp_data.mean()
                std_temp = temp_data.std()
                outliers = temp_data[(temp_data < mean_temp - 3*std_temp) | 
                                   (temp_data > mean_temp + 3*std_temp)]
                print(f"  异常值数量: {len(outliers)}")
                print(f"  温度范围: {temp_data.min():.1f}°C - {temp_data.max():.1f}°C")
            print()
    
    # 分析正面与背面温度的关系
    print("=== 正面与背面温度关系分析 ===")
    
    # 遮阳罩：表面 vs 背面
    if '遮阳罩表面温度' in df.columns and '遮阳罩背面温度' in df.columns:
        analyze_front_back_relationship(df, '遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩')
    
    # 制冷帐篷：表面 vs 背面
    if '制冷帐篷表面温度' in df.columns and '制冷帐篷背面温度' in df.columns:
        analyze_front_back_relationship(df, '制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷')
    
    # 皮革：表面 vs 背面
    if '皮革表面温度' in df.columns and '皮革背面温度' in df.columns:
        analyze_front_back_relationship(df, '皮革表面温度', '皮革背面温度', '皮革')
    
    # 创建数据缺失可视化
    create_missing_data_visualization(df, temp_columns, target_index)
    
    # 创建温度关系可视化
    create_temperature_relationship_plots(df)
    
    return df, target_index

def analyze_front_back_relationship(df, front_col, back_col, device_name):
    """分析正面与背面温度的关系"""
    
    # 获取有效数据
    valid_data = df[[front_col, back_col]].dropna()
    
    if len(valid_data) < 10:
        print(f"{device_name}: 有效数据点太少，无法分析关系")
        return
    
    # 计算相关系数
    correlation = valid_data[front_col].corr(valid_data[back_col])
    
    # 线性回归分析
    from scipy import stats
    slope, intercept, r_value, p_value, std_err = stats.linregress(
        valid_data[back_col], valid_data[front_col]
    )
    
    print(f"{device_name}:")
    print(f"  相关系数: {correlation:.4f}")
    print(f"  线性关系: {front_col} = {slope:.4f} × {back_col} + {intercept:.4f}")
    print(f"  R²值: {r_value**2:.4f}")
    print(f"  标准误差: {std_err:.4f}")
    
    # 计算平均温差
    temp_diff = valid_data[front_col] - valid_data[back_col]
    print(f"  平均温差: {temp_diff.mean():.2f}°C ± {temp_diff.std():.2f}°C")
    print()
    
    return slope, intercept, correlation

def create_missing_data_visualization(df, temp_columns, target_index):
    """创建数据缺失情况的可视化"""
    
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('测试13数据完整性分析 - 下午2点后数据状况', fontsize=16, fontweight='bold')
    
    for i, col in enumerate(temp_columns):
        row = i // 3
        col_idx = i % 3
        ax = axes[row, col_idx]
        
        if col in df.columns:
            # 绘制温度曲线
            ax.plot(df['真实时间'], df[col], 'b-', alpha=0.7, linewidth=1)
            
            # 标记缺失数据
            missing_mask = df[col].isna()
            if missing_mask.any():
                ax.scatter(df.loc[missing_mask, '真实时间'], 
                          [ax.get_ylim()[1]] * missing_mask.sum(),
                          color='red', marker='x', s=50, label='缺失数据')
            
            # 标记下午2点
            target_time = df.loc[target_index, '真实时间']
            ax.axvline(x=target_time, color='red', linestyle='--', alpha=0.7, label='下午2点')
            
            ax.set_title(col, fontsize=10)
            ax.set_ylabel('温度 (°C)', fontsize=9)
            ax.grid(True, alpha=0.3)
            
            # 设置时间格式
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            ax.tick_params(axis='x', rotation=45, labelsize=8)
            
            if missing_mask.any():
                ax.legend(fontsize=8)
    
    plt.tight_layout()
    plt.savefig('测试13数据完整性分析.png', dpi=300, bbox_inches='tight')
    print("数据完整性分析图已保存为: 测试13数据完整性分析.png")
    plt.close()

def create_temperature_relationship_plots(df):
    """创建温度关系分析图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('正面与背面温度关系分析', fontsize=16, fontweight='bold')
    
    relationships = [
        ('遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩'),
        ('制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷'),
        ('皮革表面温度', '皮革背面温度', '皮革'),
        ('环境温度', '皮革背面温度', '环境vs皮革背面')  # 作为对比
    ]
    
    for i, (front_col, back_col, title) in enumerate(relationships):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        if front_col in df.columns and back_col in df.columns:
            valid_data = df[[front_col, back_col]].dropna()
            
            if len(valid_data) > 0:
                # 散点图
                ax.scatter(valid_data[back_col], valid_data[front_col], 
                          alpha=0.6, s=20)
                
                # 拟合直线
                from scipy import stats
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    valid_data[back_col], valid_data[front_col]
                )
                
                x_line = np.linspace(valid_data[back_col].min(), 
                                   valid_data[back_col].max(), 100)
                y_line = slope * x_line + intercept
                ax.plot(x_line, y_line, 'r-', alpha=0.8, 
                       label=f'y = {slope:.3f}x + {intercept:.2f}\nR² = {r_value**2:.3f}')
                
                ax.set_xlabel(f'{back_col} (°C)', fontsize=10)
                ax.set_ylabel(f'{front_col} (°C)', fontsize=10)
                ax.set_title(title, fontsize=12)
                ax.grid(True, alpha=0.3)
                ax.legend()
    
    plt.tight_layout()
    plt.savefig('温度关系分析.png', dpi=300, bbox_inches='tight')
    print("温度关系分析图已保存为: 温度关系分析.png")
    plt.close()

if __name__ == "__main__":
    df, target_index = analyze_test13_data()
    print("\n=== 诊断分析完成 ===")
    print("请查看生成的分析图表以了解具体的数据问题")
