"""
测试13关键温度参数对比分析脚本
================================

功能说明：
为测试13数据创建专门的温度对比折线图，重点关注三个关键温度参数：
- 制冷帐篷表面温度
- 遮阳罩表面温度  
- 环境温度

输出文件：测试13_关键温度对比.png

作者：AI助手
日期：2025年
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免弹出窗口
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
from datetime import datetime, timedelta
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False
rcParams['figure.facecolor'] = 'white'
rcParams['axes.facecolor'] = 'white'

def create_test13_temperature_comparison():
    """创建测试13的关键温度参数对比图"""
    
    # 读取测试13数据
    try:
        df = pd.read_csv('13_fixed_headers.csv')
        print(f"成功读取测试13数据，共 {len(df)} 个数据点")
    except Exception as e:
        print(f"读取数据文件时出错: {e}")
        return

    # 设置测试开始时间（基于xlsx文件创建时间：2025年7月7日17:26）
    # 假设测试在文件创建后不久开始，设定为17:30开始
    test_start_time = datetime(2025, 7, 7, 17, 30, 0)
    print(f"测试开始时间设定为: {test_start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 将相对时间（秒）转换为真实时间戳
    df['真实时间'] = df['时间'].apply(lambda x: test_start_time + timedelta(seconds=x))

    # 计算测试总时长
    total_duration = df['时间'].max()
    duration_hours = total_duration / 3600
    print(f"测试总时长: {total_duration:.0f}秒 ({duration_hours:.1f}小时)")
    
    # 定义要分析的三个关键温度参数
    key_temperatures = {
        '制冷帐篷表面温度': {
            'column': '制冷帐篷表面温度',
            'color': '#2E86AB',      # 深蓝色 - 代表制冷效果
            'linestyle': '-',
            'linewidth': 2.5,
            'marker': 'o'
        },
        '遮阳罩表面温度': {
            'column': '遮阳罩表面温度', 
            'color': '#F24236',      # 红色 - 代表受热表面
            'linestyle': '--',
            'linewidth': 2.5,
            'marker': 's'
        },
        '环境温度': {
            'column': '环境温度',
            'color': '#F6AE2D',      # 橙黄色 - 代表环境基准
            'linestyle': '-.',
            'linewidth': 2.5,
            'marker': '^'
        }
    }
    
    # 检查数据列是否存在
    missing_columns = []
    for temp_name, temp_info in key_temperatures.items():
        if temp_info['column'] not in df.columns:
            missing_columns.append(temp_info['column'])
    
    if missing_columns:
        print(f"警告：以下温度列不存在于数据中: {missing_columns}")
        return
    
    # 数据采样以提高性能（每12个点取一个样本）
    sample_interval = 12
    sample_df = df.iloc[::sample_interval].copy()
    print(f"数据采样后，使用 {len(sample_df)} 个数据点进行绘图")
    
    # 创建图表
    plt.figure(figsize=(16, 10))
    
    # 绘制三条温度曲线
    for temp_name, temp_info in key_temperatures.items():
        temp_column = temp_info['column']
        
        # 绘制主要曲线
        plt.plot(sample_df['时间_分钟'], sample_df[temp_column],
                color=temp_info['color'],
                linestyle=temp_info['linestyle'],
                linewidth=temp_info['linewidth'],
                label=temp_name,
                alpha=0.9)
        
        # 添加数据点标记（仅在数据点较少时显示）
        if len(sample_df) <= 100:
            plt.scatter(sample_df['时间_分钟'], sample_df[temp_column],
                       color=temp_info['color'],
                       marker=temp_info['marker'],
                       s=25,
                       alpha=0.7,
                       edgecolors='white',
                       linewidth=0.5)
    
    # 设置图表标题和标签
    plt.title('测试13 - 关键温度参数对比', fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('时间 (分钟)', fontsize=14, fontweight='bold')
    plt.ylabel('温度 (°C)', fontsize=14, fontweight='bold')
    
    # 设置网格
    plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.8)
    plt.grid(True, alpha=0.15, linestyle=':', linewidth=0.5, which='minor')
    
    # 设置Y轴范围
    all_temps = []
    for temp_info in key_temperatures.values():
        temp_data = df[temp_info['column']].dropna()
        all_temps.extend(temp_data.tolist())
    
    if all_temps:
        y_min = min(all_temps) - 1
        y_max = max(all_temps) + 1
        plt.ylim(y_min, y_max)
        print(f"温度范围: {y_min:.1f}°C 到 {y_max:.1f}°C")
    
    # 设置图例
    legend = plt.legend(loc='upper right', 
                       fontsize=12,
                       frameon=True,
                       fancybox=True,
                       shadow=True,
                       borderpad=1,
                       columnspacing=1)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)
    
    # 设置坐标轴样式
    plt.gca().spines['top'].set_visible(False)
    plt.gca().spines['right'].set_visible(False)
    plt.gca().spines['left'].set_linewidth(1.2)
    plt.gca().spines['bottom'].set_linewidth(1.2)
    
    # 设置刻度样式
    plt.xticks(fontsize=11)
    plt.yticks(fontsize=11)
    
    # 添加次要刻度
    plt.minorticks_on()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_filename = '测试13_关键温度对比.png'
    plt.savefig(output_filename, 
                dpi=300, 
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                pad_inches=0.2)
    
    print(f"图表已保存为: {output_filename}")
    
    # 关闭图形以释放内存
    plt.close()
    
    # 输出数据统计信息
    print("\n=== 测试13关键温度参数统计摘要 ===")
    for temp_name, temp_info in key_temperatures.items():
        temp_column = temp_info['column']
        temp_data = df[temp_column].dropna()
        
        print(f"\n{temp_name}:")
        print(f"  平均温度: {temp_data.mean():.2f}°C")
        print(f"  最高温度: {temp_data.max():.2f}°C")
        print(f"  最低温度: {temp_data.min():.2f}°C")
        print(f"  温度范围: {temp_data.max() - temp_data.min():.2f}°C")
        print(f"  标准差: {temp_data.std():.2f}°C")
    
    # 计算温度差异
    制冷帐篷温度 = df['制冷帐篷表面温度'].dropna()
    遮阳罩温度 = df['遮阳罩表面温度'].dropna()
    环境温度 = df['环境温度'].dropna()
    
    print(f"\n=== 温度差异分析 ===")
    print(f"遮阳罩与环境温度平均差异: {遮阳罩温度.mean() - 环境温度.mean():.2f}°C")
    print(f"制冷帐篷与环境温度平均差异: {制冷帐篷温度.mean() - 环境温度.mean():.2f}°C")
    print(f"遮阳罩与制冷帐篷平均温差: {遮阳罩温度.mean() - 制冷帐篷温度.mean():.2f}°C")

# 主程序执行
if __name__ == "__main__":
    print("开始创建测试13关键温度参数对比图...")
    create_test13_temperature_comparison()
    print("\n=== 分析完成 ===")
    print("已生成文件: 测试13_关键温度对比.png")
    print("图表特点:")
    print("- 高分辨率输出 (300 DPI)")
    print("- 专业配色方案便于区分")
    print("- 包含详细的统计分析")
    print("- 优化的数据采样提高性能")
