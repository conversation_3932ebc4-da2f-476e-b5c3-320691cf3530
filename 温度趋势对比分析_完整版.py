"""
温度趋势对比分析脚本
====================

功能说明：
1. 读取四个测试的温度数据文件（5, 11, 12, 13号测试）
2. 生成多种类型的温度对比分析图表
3. 计算并保存详细的统计摘要数据

生成的图表：
- 温度趋势对比分析.png：四个测试的主要对比图（2x2子图）
- 四测试综合温度对比图.png：所有测试数据的综合对比
- 四测试分组温度对比图.png：按温度类型分组的对比图
- 温度统计摘要对比图.png：统计数据的可视化对比

生成的数据文件：
- 温度统计摘要详细.csv：包含平均值、最大值、最小值、标准差等统计信息

使用方法：
直接运行此脚本：py 温度趋势对比分析_完整版.py

作者：AI助手
日期：2025年
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 定义文件列表和对应的测试名称
files = [
    '5_fixed_headers.csv',
    '11_fixed_headers.csv', 
    '12_fixed_headers.csv',
    '13_fixed_headers.csv'
]

test_names = ['测试5', '测试11', '测试12', '测试13']

# 定义要绘制的温度列和分组
temperature_groups = {
    '遮阳罩温度': ['遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度'],
    '制冷帐篷温度': ['制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度'],
    '皮革温度': ['皮革表面温度', '皮革背面温度'],
    '环境温度': ['环境温度']
}

temperature_columns = [col for group in temperature_groups.values() for col in group]

# 定义颜色方案
colors = {
    '遮阳罩表面温度': '#E74C3C',
    '遮阳罩背面温度': '#F39C12',
    '遮阳罩皮革表面温度': '#D35400',
    '制冷帐篷表面温度': '#3498DB',
    '制冷帐篷背面温度': '#2980B9',
    '制冷帐篷皮革温度': '#1ABC9C',
    '皮革表面温度': '#9B59B6',
    '皮革背面温度': '#8E44AD',
    '环境温度': '#27AE60'
}

line_styles = ['-', '--', '-.', ':']
markers = ['o', 's', '^', 'D']

def load_data():
    """加载所有测试数据"""
    all_data = {}
    all_temps = []
    
    for file, test_name in zip(files, test_names):
        try:
            df = pd.read_csv(file)
            df['时间_分钟'] = df['时间'] / 60
            all_data[test_name] = df
            
            for temp_col in temperature_columns:
                if temp_col in df.columns:
                    all_temps.extend(df[temp_col].dropna().tolist())
                    
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")
    
    return all_data, all_temps

def create_main_chart(all_data, all_temps):
    """创建主要的温度趋势对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('温度趋势对比分析 - 四个测试详细对比', fontsize=18, fontweight='bold')
    
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3
    
    for idx, (test_name, df) in enumerate(all_data.items()):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]
        
        # 数据采样以提高性能
        sample_df = df.iloc[::10]
        
        for temp_col in temperature_columns:
            if temp_col in df.columns:
                ax.plot(sample_df['时间_分钟'], sample_df[temp_col],
                       color=colors[temp_col],
                       linewidth=2,
                       label=temp_col,
                       alpha=0.8)
        
        ax.set_title(f'{test_name} 温度变化趋势', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.set_ylim(y_min, y_max)
        ax.set_facecolor('#FAFAFA')
        
        ax.legend(bbox_to_anchor=(0.5, -0.1), loc='upper center',
                 fontsize=9, ncol=3)
    
    plt.tight_layout()
    plt.savefig('温度趋势对比分析.png', dpi=300, bbox_inches='tight')
    print("主要对比图已保存为 '温度趋势对比分析.png'")
    plt.close()

def create_comprehensive_chart(all_data, all_temps):
    """创建综合对比图"""
    plt.figure(figsize=(24, 12))
    
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3
    
    for test_idx, (test_name, df) in enumerate(all_data.items()):
        sample_df = df.iloc[::15]
        
        for temp_col in temperature_columns:
            if temp_col in df.columns:
                plt.plot(sample_df['时间_分钟'], sample_df[temp_col],
                        color=colors[temp_col],
                        linestyle=line_styles[test_idx],
                        linewidth=2,
                        label=f'{test_name}-{temp_col}',
                        alpha=0.7)
    
    plt.title('四个测试所有温度参数综合对比分析', fontsize=16, fontweight='bold')
    plt.xlabel('时间 (分钟)', fontsize=14)
    plt.ylabel('温度 (°C)', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.ylim(y_min, y_max)
    plt.gca().set_facecolor('#FAFAFA')
    
    plt.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=8, ncol=4)
    plt.tight_layout()
    
    plt.savefig('四测试综合温度对比图.png', dpi=300, bbox_inches='tight')
    print("综合对比图已保存为 '四测试综合温度对比图.png'")
    plt.close()

def create_grouped_chart(all_data, all_temps):
    """创建按温度类型分组的对比图"""
    fig, axes = plt.subplots(3, 3, figsize=(22, 18))
    fig.suptitle('四个测试按温度类型分组对比分析', fontsize=18, fontweight='bold')
    
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3
    
    for temp_idx, temp_col in enumerate(temperature_columns):
        row = temp_idx // 3
        col = temp_idx % 3
        ax = axes[row, col]
        
        for test_idx, (test_name, df) in enumerate(all_data.items()):
            if temp_col in df.columns:
                sample_df = df.iloc[::20]
                ax.plot(sample_df['时间_分钟'], sample_df[temp_col],
                       linestyle=line_styles[test_idx],
                       linewidth=2.5,
                       label=test_name,
                       alpha=0.85)
        
        ax.set_title(temp_col, fontsize=11, fontweight='bold')
        ax.set_xlabel('时间 (分钟)', fontsize=9)
        ax.set_ylabel('温度 (°C)', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)
        ax.set_ylim(y_min, y_max)
        ax.set_facecolor('#FAFAFA')
    
    plt.tight_layout()
    plt.savefig('四测试分组温度对比图.png', dpi=300, bbox_inches='tight')
    print("分组对比图已保存为 '四测试分组温度对比图.png'")
    plt.close()

def create_statistical_summary(all_data):
    """创建统计摘要图和数据"""
    # 准备统计数据
    stats_data = []
    for test_name, df in all_data.items():
        for temp_col in temperature_columns:
            if temp_col in df.columns:
                temp_data = df[temp_col].dropna()
                stats_data.append({
                    '测试': test_name,
                    '温度类型': temp_col,
                    '平均值': temp_data.mean(),
                    '最大值': temp_data.max(),
                    '最小值': temp_data.min(),
                    '标准差': temp_data.std(),
                    '温度范围': temp_data.max() - temp_data.min()
                })
    
    stats_df = pd.DataFrame(stats_data)
    
    # 创建统计图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 14))
    fig.suptitle('温度数据统计摘要分析', fontsize=18, fontweight='bold')
    
    # 1. 平均温度对比
    pivot_mean = stats_df.pivot(index='温度类型', columns='测试', values='平均值')
    pivot_mean.plot(kind='bar', ax=ax1, width=0.8, alpha=0.8)
    ax1.set_title('各测试平均温度对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(title='测试')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. 温度范围对比
    pivot_range = stats_df.pivot(index='温度类型', columns='测试', values='温度范围')
    pivot_range.plot(kind='bar', ax=ax2, width=0.8, alpha=0.8)
    ax2.set_title('各测试温度变化范围对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('温度范围 (°C)', fontsize=12)
    ax2.legend(title='测试')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)
    
    # 3. 标准差对比
    pivot_std = stats_df.pivot(index='温度类型', columns='测试', values='标准差')
    pivot_std.plot(kind='bar', ax=ax3, width=0.8, alpha=0.8)
    ax3.set_title('各测试温度稳定性对比 (标准差)', fontsize=14, fontweight='bold')
    ax3.set_ylabel('标准差 (°C)', fontsize=12)
    ax3.legend(title='测试')
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. 最高温度对比
    pivot_max = stats_df.pivot(index='温度类型', columns='测试', values='最大值')
    pivot_max.plot(kind='bar', ax=ax4, width=0.8, alpha=0.8)
    ax4.set_title('各测试最高温度对比', fontsize=14, fontweight='bold')
    ax4.set_ylabel('温度 (°C)', fontsize=12)
    ax4.legend(title='测试')
    ax4.grid(True, alpha=0.3)
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('温度统计摘要对比图.png', dpi=300, bbox_inches='tight')
    print("统计摘要图已保存为 '温度统计摘要对比图.png'")
    plt.close()
    
    # 保存统计数据
    stats_df.to_csv('温度统计摘要详细.csv', index=False, encoding='utf-8-sig')
    print("统计摘要数据已保存为 '温度统计摘要详细.csv'")
    
    return stats_df

# 主程序
if __name__ == "__main__":
    print("开始生成温度趋势对比分析图...")
    
    # 加载数据
    all_data, all_temps = load_data()
    
    if not all_data:
        print("没有找到有效的数据文件！")
        exit(1)
    
    print(f"成功加载 {len(all_data)} 个测试的数据")
    
    # 创建各种图表
    create_main_chart(all_data, all_temps)
    create_comprehensive_chart(all_data, all_temps)
    create_grouped_chart(all_data, all_temps)
    stats_summary = create_statistical_summary(all_data)
    
    print("\n=== 温度趋势对比分析完成 ===")
    print("已生成以下文件:")
    print("1. 温度趋势对比分析.png - 主要四测试对比图")
    print("2. 四测试综合温度对比图.png - 综合对比图")
    print("3. 四测试分组温度对比图.png - 按温度类型分组图")
    print("4. 温度统计摘要对比图.png - 统计摘要图")
    print("5. 温度统计摘要详细.csv - 详细统计数据")
