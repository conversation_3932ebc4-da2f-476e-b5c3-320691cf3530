import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
try:
    plt.style.use('seaborn-v0_8-whitegrid')
except:
    try:
        plt.style.use('seaborn-whitegrid')
    except:
        plt.style.use('default')

rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False
rcParams['figure.facecolor'] = 'white'
rcParams['axes.facecolor'] = 'white'

# 定义文件列表和对应的测试名称
files = [
    '5_fixed_headers.csv',
    '11_fixed_headers.csv',
    '12_fixed_headers.csv',
    '13_fixed_headers.csv'
]

test_names = ['测试5', '测试11', '测试12', '测试13']

# 定义要绘制的温度列和分组
temperature_groups = {
    '遮阳罩温度': ['遮阳罩表面温度', '遮阳罩背面温度', '遮阳罩皮革表面温度'],
    '制冷帐篷温度': ['制冷帐篷表面温度', '制冷帐篷背面温度', '制冷帐篷皮革温度'],
    '皮革温度': ['皮革表面温度', '皮革背面温度'],
    '环境温度': ['环境温度']
}

# 所有温度列
temperature_columns = [col for group in temperature_groups.values() for col in group]

# 定义更加专业的颜色方案
colors = {
    '遮阳罩表面温度': '#E74C3C',    # 深红色
    '遮阳罩背面温度': '#F39C12',     # 橙色
    '遮阳罩皮革表面温度': '#D35400', # 深橙色
    '制冷帐篷表面温度': '#3498DB',   # 蓝色
    '制冷帐篷背面温度': '#2980B9',   # 深蓝色
    '制冷帐篷皮革温度': '#1ABC9C',   # 青绿色
    '皮革表面温度': '#9B59B6',       # 紫色
    '皮革背面温度': '#8E44AD',       # 深紫色
    '环境温度': '#27AE60'            # 绿色
}

# 线型样式
line_styles = ['-', '--', '-.', ':']
markers = ['o', 's', '^', 'D']

# 数据预处理函数
def load_and_process_data():
    """加载并预处理所有测试数据"""
    all_data = {}
    all_temps = []

    for file, test_name in zip(files, test_names):
        try:
            df = pd.read_csv(file)
            # 将时间转换为分钟以便更好显示
            df['时间_分钟'] = df['时间'] / 60
            all_data[test_name] = df

            # 收集所有温度值用于设置Y轴范围
            for temp_col in temperature_columns:
                if temp_col in df.columns:
                    all_temps.extend(df[temp_col].dropna().tolist())

        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")

    return all_data, all_temps

# 创建主要的温度趋势对比分析图
def create_main_comparison_chart(all_data, all_temps):
    """创建主要的四测试对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('温度趋势对比分析 - 四个测试详细对比', fontsize=18, fontweight='bold', y=0.95)

    # 计算Y轴范围
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3

    for idx, (test_name, df) in enumerate(all_data.items()):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]

        # 按组绘制温度曲线
        for group_name, temp_cols in temperature_groups.items():
            for temp_col in temp_cols:
                if temp_col in df.columns:
                    # 每隔10个点取样以减少数据密度
                    sample_df = df.iloc[::10]
                    ax.plot(sample_df['时间_分钟'], sample_df[temp_col],
                           color=colors[temp_col],
                           linewidth=2.5,
                           label=temp_col,
                           alpha=0.85,
                           marker='o' if len(sample_df) < 50 else None,
                           markersize=3)

        # 设置子图样式
        ax.set_title(f'{test_name} 温度变化趋势', fontsize=14, fontweight='bold', pad=15)
        ax.set_xlabel('时间 (分钟)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.set_ylim(y_min, y_max)

        # 添加背景色区分
        ax.set_facecolor('#FAFAFA')

        # 图例设置
        ax.legend(bbox_to_anchor=(0.5, -0.12), loc='upper center',
                 fontsize=9, ncol=3, frameon=True, fancybox=True,
                 shadow=True, borderaxespad=0)

    plt.tight_layout(rect=[0, 0.08, 1, 0.93])

    # 保存图片
    plt.savefig('温度趋势对比分析.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('温度趋势对比分析.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("主要对比图已保存为 '温度趋势对比分析.png' 和 '温度趋势对比分析.pdf'")
    plt.show()

# 加载数据
all_data, all_temps = load_and_process_data()

# 创建主要对比图
create_main_comparison_chart(all_data, all_temps)

# 创建综合对比图
def create_comprehensive_comparison(all_data, all_temps):
    """创建所有测试的综合对比图"""
    plt.figure(figsize=(24, 14))

    # 计算Y轴范围
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3

    # 为每个测试分配线型和透明度
    alphas = [0.9, 0.8, 0.7, 0.6]

    # 绘制所有数据
    for test_idx, (test_name, df) in enumerate(all_data.items()):
        # 数据采样以提高性能
        sample_df = df.iloc[::15]  # 每15个点取一个样本

        for temp_col in temperature_columns:
            if temp_col in df.columns:
                plt.plot(sample_df['时间_分钟'], sample_df[temp_col],
                        color=colors[temp_col],
                        linestyle=line_styles[test_idx],
                        linewidth=2.2,
                        label=f'{test_name}-{temp_col}',
                        alpha=alphas[test_idx])

    plt.title('四个测试所有温度参数综合对比分析', fontsize=18, fontweight='bold', pad=20)
    plt.xlabel('时间 (分钟)', fontsize=14)
    plt.ylabel('温度 (°C)', fontsize=14)
    plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    plt.ylim(y_min, y_max)

    # 设置背景
    plt.gca().set_facecolor('#FAFAFA')

    # 创建图例 - 分多列显示
    plt.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=8, ncol=4,
              frameon=True, fancybox=True, shadow=True)

    plt.tight_layout()

    # 保存图片
    plt.savefig('四测试综合温度对比图.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('四测试综合温度对比图.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("综合对比图已保存为 '四测试综合温度对比图.png' 和 '四测试综合温度对比图.pdf'")
    plt.show()

# 创建综合对比图
create_comprehensive_comparison(all_data, all_temps)

# 创建按温度类型分组的对比图
def create_grouped_comparison(all_data, all_temps):
    """创建按温度类型分组的对比图"""
    fig, axes = plt.subplots(3, 3, figsize=(22, 18))
    fig.suptitle('四个测试按温度类型分组对比分析', fontsize=18, fontweight='bold', y=0.96)

    # 计算Y轴范围
    y_min = min(all_temps) - 3
    y_max = max(all_temps) + 3

    # 将9个温度类型分配到3x3的子图中
    for temp_idx, temp_col in enumerate(temperature_columns):
        row = temp_idx // 3
        col = temp_idx % 3
        ax = axes[row, col]

        # 为每个测试绘制该温度类型的曲线
        for test_idx, (test_name, df) in enumerate(all_data.items()):
            if temp_col in df.columns:
                # 数据采样
                sample_df = df.iloc[::20]
                ax.plot(sample_df['时间_分钟'], sample_df[temp_col],
                       linestyle=line_styles[test_idx],
                       linewidth=2.5,
                       label=test_name,
                       alpha=0.85,
                       marker=markers[test_idx] if len(sample_df) < 30 else None,
                       markersize=4)

        # 设置子图样式
        ax.set_title(temp_col, fontsize=11, fontweight='bold', pad=10)
        ax.set_xlabel('时间 (分钟)', fontsize=9)
        ax.set_ylabel('温度 (°C)', fontsize=9)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.legend(fontsize=8, loc='best')
        ax.set_ylim(y_min, y_max)
        ax.set_facecolor('#FAFAFA')

    plt.tight_layout(rect=[0, 0.02, 1, 0.94])

    # 保存图片
    plt.savefig('四测试分组温度对比图.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('四测试分组温度对比图.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("分组对比图已保存为 '四测试分组温度对比图.png' 和 '四测试分组温度对比图.pdf'")
    plt.show()

# 创建统计摘要图
def create_statistical_summary(all_data, all_temps):
    """创建统计摘要对比图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 14))
    fig.suptitle('温度数据统计摘要分析', fontsize=18, fontweight='bold', y=0.95)

    # 准备统计数据
    stats_data = []
    for test_name, df in all_data.items():
        for temp_col in temperature_columns:
            if temp_col in df.columns:
                temp_data = df[temp_col].dropna()
                stats_data.append({
                    '测试': test_name,
                    '温度类型': temp_col,
                    '平均值': temp_data.mean(),
                    '最大值': temp_data.max(),
                    '最小值': temp_data.min(),
                    '标准差': temp_data.std()
                })

    stats_df = pd.DataFrame(stats_data)

    # 1. 平均温度对比
    pivot_mean = stats_df.pivot(index='温度类型', columns='测试', values='平均值')
    pivot_mean.plot(kind='bar', ax=ax1, width=0.8, alpha=0.8)
    ax1.set_title('各测试平均温度对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('温度 (°C)', fontsize=12)
    ax1.legend(title='测试', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # 2. 温度范围对比
    temp_ranges = stats_df.groupby(['测试', '温度类型']).apply(
        lambda x: x['最大值'].iloc[0] - x['最小值'].iloc[0]
    ).reset_index(name='温度范围')
    pivot_range = temp_ranges.pivot(index='温度类型', columns='测试', values='温度范围')
    pivot_range.plot(kind='bar', ax=ax2, width=0.8, alpha=0.8)
    ax2.set_title('各测试温度变化范围对比', fontsize=14, fontweight='bold')
    ax2.set_ylabel('温度范围 (°C)', fontsize=12)
    ax2.legend(title='测试', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)

    # 3. 标准差对比
    pivot_std = stats_df.pivot(index='温度类型', columns='测试', values='标准差')
    pivot_std.plot(kind='bar', ax=ax3, width=0.8, alpha=0.8)
    ax3.set_title('各测试温度稳定性对比 (标准差)', fontsize=14, fontweight='bold')
    ax3.set_ylabel('标准差 (°C)', fontsize=12)
    ax3.legend(title='测试', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(axis='x', rotation=45)

    # 4. 最高温度对比
    pivot_max = stats_df.pivot(index='温度类型', columns='测试', values='最大值')
    pivot_max.plot(kind='bar', ax=ax4, width=0.8, alpha=0.8)
    ax4.set_title('各测试最高温度对比', fontsize=14, fontweight='bold')
    ax4.set_ylabel('温度 (°C)', fontsize=12)
    ax4.legend(title='测试', bbox_to_anchor=(1.05, 1), loc='upper left')
    ax4.grid(True, alpha=0.3)
    ax4.tick_params(axis='x', rotation=45)

    plt.tight_layout(rect=[0, 0.02, 1, 0.93])

    # 保存图片
    plt.savefig('温度统计摘要对比图.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    plt.savefig('温度统计摘要对比图.pdf', bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("统计摘要图已保存为 '温度统计摘要对比图.png' 和 '温度统计摘要对比图.pdf'")
    plt.show()

    return stats_df

# 创建分组对比图
create_grouped_comparison(all_data, all_temps)

# 创建统计摘要图
stats_summary = create_statistical_summary(all_data, all_temps)

# 保存统计数据到CSV
stats_summary.to_csv('温度统计摘要详细.csv', index=False, encoding='utf-8-sig')
print("统计摘要数据已保存为 '温度统计摘要详细.csv'")

print("\n=== 温度趋势对比分析完成 ===")
print("已生成以下文件:")
print("1. 温度趋势对比分析.png/pdf - 主要四测试对比图")
print("2. 四测试综合温度对比图.png/pdf - 综合对比图")
print("3. 四测试分组温度对比图.png/pdf - 按温度类型分组图")
print("4. 温度统计摘要对比图.png/pdf - 统计摘要图")
print("5. 温度统计摘要详细.csv - 详细统计数据")
