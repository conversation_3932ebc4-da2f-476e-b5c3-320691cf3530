import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 定义文件列表和对应的测试名称
files = [
    '5_fixed_headers.csv',
    '11_fixed_headers.csv', 
    '12_fixed_headers.csv',
    '13_fixed_headers.csv'
]

test_names = ['测试5', '测试11', '测试12', '测试13']

# 定义要绘制的温度列
temperature_columns = [
    '遮阳罩表面温度',
    '遮阳罩背面温度', 
    '遮阳罩皮革表面温度',
    '制冷帐篷表面温度',
    '制冷帐篷背面温度',
    '制冷帐篷皮革温度',
    '皮革表面温度',
    '皮革背面温度',
    '环境温度'
]

# 定义颜色方案 - 为每个温度类型分配不同颜色
colors = [
    '#FF6B6B',  # 遮阳罩表面温度 - 红色
    '#4ECDC4',  # 遮阳罩背面温度 - 青色
    '#45B7D1',  # 遮阳罩皮革表面温度 - 蓝色
    '#96CEB4',  # 制冷帐篷表面温度 - 绿色
    '#FFEAA7',  # 制冷帐篷背面温度 - 黄色
    '#DDA0DD',  # 制冷帐篷皮革温度 - 紫色
    '#98D8C8',  # 皮革表面温度 - 薄荷绿
    '#F7DC6F',  # 皮革背面温度 - 金黄色
    '#FF9F43'   # 环境温度 - 橙色
]

# 创建大图 - 增加图片尺寸以容纳图例
fig, axes = plt.subplots(2, 2, figsize=(24, 18))
fig.suptitle('四个测试温度对比分析', fontsize=20, fontweight='bold', y=0.95)

# 读取并处理每个测试数据
all_temps = []  # 用于记录所有温度值，以便设置合适的Y轴范围

for idx, (file, test_name) in enumerate(zip(files, test_names)):
    try:
        # 读取数据
        df = pd.read_csv(file)

        # 记录温度数据用于设置Y轴范围
        for temp_col in temperature_columns:
            if temp_col in df.columns:
                all_temps.extend(df[temp_col].dropna().tolist())

        # 确定子图位置
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]

        # 绘制每个温度曲线
        for temp_idx, temp_col in enumerate(temperature_columns):
            if temp_col in df.columns:
                ax.plot(df['时间'], df[temp_col],
                       color=colors[temp_idx],
                       linewidth=2.5,
                       label=temp_col,
                       alpha=0.8)

        # 设置子图标题和标签
        ax.set_title(f'{test_name} - 温度变化曲线', fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('时间 (秒)', fontsize=12)
        ax.set_ylabel('温度 (°C)', fontsize=12)
        ax.grid(True, alpha=0.3, linestyle='--')

        # 将图例放在子图下方，避免遮挡曲线
        ax.legend(bbox_to_anchor=(0.5, -0.15), loc='upper center',
                 fontsize=9, ncol=3, frameon=True, fancybox=True, shadow=True)

    except Exception as e:
        print(f"处理文件 {file} 时出错: {e}")

# 设置统一的Y轴范围
if all_temps:
    y_min = min(all_temps) - 2
    y_max = max(all_temps) + 2
    for ax in axes.flat:
        ax.set_ylim(y_min, y_max)

# 调整布局，为图例留出空间
plt.tight_layout(rect=[0, 0.1, 1, 0.93])

# 保存图片
plt.savefig('四测试温度对比折线图.png', dpi=300, bbox_inches='tight')
plt.savefig('四测试温度对比折线图.pdf', bbox_inches='tight')

print("图表已保存为 '四测试温度对比折线图.png' 和 '四测试温度对比折线图.pdf'")

# 显示图表
plt.show()

# 创建单独的综合对比图 - 增大尺寸
plt.figure(figsize=(28, 16))

# 为每个测试分配线型和透明度
line_styles = ['-', '--', '-.', ':']
alphas = [0.9, 0.8, 0.7, 0.6]

# 读取所有数据并在同一图中绘制
for test_idx, (file, test_name) in enumerate(zip(files, test_names)):
    try:
        df = pd.read_csv(file)

        # 为每个温度类型绘制该测试的数据
        for temp_idx, temp_col in enumerate(temperature_columns):
            if temp_col in df.columns:
                plt.plot(df['时间'], df[temp_col],
                        color=colors[temp_idx],
                        linestyle=line_styles[test_idx],
                        linewidth=2.5,
                        label=f'{test_name}-{temp_col}',
                        alpha=alphas[test_idx])

    except Exception as e:
        print(f"处理文件 {file} 时出错: {e}")

plt.title('四个测试所有温度参数综合对比', fontsize=20, fontweight='bold', pad=20)
plt.xlabel('时间 (秒)', fontsize=16)
plt.ylabel('温度 (°C)', fontsize=16)
plt.grid(True, alpha=0.3, linestyle='--')

# 设置Y轴范围
if all_temps:
    plt.ylim(y_min, y_max)

# 创建图例 - 分多列显示，放在图的右侧
plt.legend(bbox_to_anchor=(1.02, 1), loc='upper left', fontsize=9, ncol=4,
          frameon=True, fancybox=True, shadow=True)

plt.tight_layout()

# 保存综合对比图
plt.savefig('四测试综合温度对比图.png', dpi=300, bbox_inches='tight')
plt.savefig('四测试综合温度对比图.pdf', bbox_inches='tight')

print("综合对比图已保存为 '四测试综合温度对比图.png' 和 '四测试综合温度对比图.pdf'")

plt.show()

# 创建按温度类型分组的对比图
fig, axes = plt.subplots(3, 3, figsize=(24, 20))
fig.suptitle('四个测试按温度类型分组对比', fontsize=20, fontweight='bold', y=0.95)

# 将9个温度类型分配到3x3的子图中
for temp_idx, temp_col in enumerate(temperature_columns):
    row = temp_idx // 3
    col = temp_idx % 3
    ax = axes[row, col]

    # 为每个测试绘制该温度类型的曲线
    for test_idx, (file, test_name) in enumerate(zip(files, test_names)):
        try:
            df = pd.read_csv(file)
            if temp_col in df.columns:
                ax.plot(df['时间'], df[temp_col],
                       linestyle=line_styles[test_idx],
                       linewidth=2.5,
                       label=test_name,
                       alpha=0.8)
        except Exception as e:
            print(f"处理文件 {file} 时出错: {e}")

    ax.set_title(temp_col, fontsize=12, fontweight='bold')
    ax.set_xlabel('时间 (秒)', fontsize=10)
    ax.set_ylabel('温度 (°C)', fontsize=10)
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.legend(fontsize=9)

    # 设置Y轴范围
    if all_temps:
        ax.set_ylim(y_min, y_max)

plt.tight_layout(rect=[0, 0.02, 1, 0.93])

# 保存分组对比图
plt.savefig('四测试分组温度对比图.png', dpi=300, bbox_inches='tight')
plt.savefig('四测试分组温度对比图.pdf', bbox_inches='tight')

print("分组对比图已保存为 '四测试分组温度对比图.png' 和 '四测试分组温度对比图.pdf'")

plt.show()
